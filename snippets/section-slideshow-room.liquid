{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a slideshow room section with dual images layout.

  Accepts:
  - full_width {boolean} - Whether to make the section full width
  - autoplay {boolean} - Whether to autoplay the slideshow
  - autoplay_speed {5-10} - The speed at which the slideshow should autoplay
  - style {'minimal'|'arrows'|'bars'|'dots'} - The style of the slideshow
  - height {450-750} - The height of the slideshow
  - height_mobile {350-650} - The height of the slideshow on mobile
  - hydration {string} - The hydration strategy for the section

  Features:
  - Left side: Carousel with multiple images (647px × 647px each)
  - Right side: Single static image (1015px × 647px)
  - Gap between images: 58px
  - Responsive design for mobile and tablet

  Usage:
  {% render 'section-slideshow-room' %}
{%- endcomment -%}

{%- liquid
  assign full_width = full_width | default: section.settings.full_width, allow_false: true | default: true, allow_false: true
  assign autoplay = autoplay | default: section.settings.autoplay, allow_false: true | default: false, allow_false: true
  assign autoplay_speed = autoplay_speed | default: section.settings.autoplay_speed | default: 7
  assign style = style | default: section.settings.style | default: 'arrows'
  assign height = height | default: section.settings.height | default: 650
  assign height_mobile = height_mobile | default: section.settings.height_mobile | default: 450
  assign hydration = hydration | default: 'on:visible'

  assign lazyload_images = true
  if section.index == 1
    assign lazyload_images = false
  endif
-%}

<is-land {{ hydration }}>
  <slideshow-section section-id="{{ section.id }}">
    {%- unless full_width -%}
      <div class="page-width hero--padded">
    {%- endunless -%}

    {% style %}
      /* Module spacing - 64px top/bottom, 100px left/right based on 1920px */
      #SlideshowWrapper-{{ section.id }} {
        padding: 3.333vw 5.208vw; /* 64px top/bottom, 100px left/right based on 1920px */
      }

      .hero--{{ section.id }} {
        height: {{ height }}px;
      }

      @media screen and (max-width: 768px) {
        .hero--{{ section.id }} {
          {% comment %}height: {{ height_mobile }}px;{% endcomment %}
        }
      }

      /* Layout control */
      .slideshow-room-mobile-layout {
        display: none;
      }

      .slideshow-room-desktop-layout {
        display: block;
      }

      /* Mobile layout styles */
      .slideshow-room-mobile-images {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 20px;
      }

      .slideshow-room-mobile-image {
        width: 100%;
        height: 250px;
        overflow: hidden;
        position: relative;
      }

      .slideshow-room-mobile-right {
        width: 100%;
        height: 250px;
        overflow: hidden;
      }

      .slideshow-room-mobile-image .hero__image,
      .slideshow-room-mobile-right .hero__image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* Dual images layout styles */
      .hero__dual-images {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 58px;
        padding: 0; /* Remove internal padding since module wrapper has spacing */
      }

      .hero__left-image {
        width: 647px;
        height: 647px;
        overflow: hidden;
        flex-shrink: 0;
        position: relative;
      }

      .hero__right-image {
        width: 1015px;
        height: 647px;
        overflow: hidden;
        flex-shrink: 0;
      }

      .hero__left-image .hero__image,
      .hero__right-image .hero__image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .hero__image-link {
        display: block;
        width: 100%;
        height: 100%;
      }

      /* Image title styles - only visible on mobile */
      .slideshow-room-image-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.6);
        color: #ffffff;
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        display: none; /* Hidden by default (desktop) */
        z-index: 5;
      }

      @media screen and (max-width: 1200px) {
        .hero__dual-images {
          gap: 30px;
          padding: 0; /* Remove internal padding since module wrapper has spacing */
        }

        .hero__left-image {
          width: 400px;
          height: 400px;
        }

        .hero__right-image {
          width: 600px;
          height: 400px;
        }

        /* Show image title on tablet */
        .slideshow-room-image-title {
          display: block;
          font-size: 1.823vw; /* 14px based on 768px */
          padding: 1.563vw 2.083vw; /* 12px 16px based on 768px */
        }
      }

      @media screen and (max-width: 768px) {
        /* Switch to mobile layout */
        .slideshow-room-mobile-layout {
          display: block !important;
        }

        .slideshow-room-desktop-layout {
          display: none !important;
        }

        /* Show image title on mobile */
        .slideshow-room-image-title {
          display: block;
          font-size: 3.733vw; /* 14px based on 375px */
          padding: 3.2vw 4.267vw; /* 12px 16px based on 375px */
        }
      }

      /* Slideshow Room Pagination Styles */
      .slideshow-room-hero--{{ section.id }} .flickity-page-dots {
        position: absolute;
        bottom: 1.042vw; /* 20px based on 1920px */
        left: 16.849vw; /* Center of left image: 323.5px / 1920px */
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.521vw; /* 10px based on 1920px */
        z-index: 10;
      }

      .slideshow-room-hero--{{ section.id }} .flickity-page-dots .dot {
        width: 0.521vw; /* 10px based on 1920px */
        height: 0.521vw; /* 10px based on 1920px */
        border-radius: 50%;
        background: #FFFFFF;
        opacity: 0.8;
        cursor: pointer;
        transition: background-color 0.3s ease;
        border: none;
        padding: 0;
        margin: 0;
      }

      .slideshow-room-hero--{{ section.id }} .flickity-page-dots .dot.is-selected {
        background: #6D4C41;
      }

      /* Medium screen responsive styles - based on 1200px */
      @media screen and (max-width: 1200px) {
        .slideshow-room-hero--{{ section.id }} .flickity-page-dots {
          left: 18.75vw; /* Center of left image: 225px / 1200px (assuming 1200px container) */
        }
      }

      /* Tablet responsive styles - based on 768px */
      @media screen and (max-width: 768px) {
        /* Hide pagination dots on mobile since we're showing all images */
        .slideshow-room-hero--{{ section.id }} .flickity-page-dots {
          display: none;
        }
      }

      /* Mobile responsive styles - based on 375px */
      @media screen and (max-width: 480px) {
        /* Hide pagination dots on mobile since we're showing all images */
        .slideshow-room-hero--{{ section.id }} .flickity-page-dots {
          display: none;
        }
      }
    {% endstyle %}

    <div id="SlideshowWrapper-{{ section.id }}">
      {%- if section.blocks.size > 0 -%}
        <div class="slideshow-wrapper">

          {%- comment -%} Mobile-specific layout: Show all left images vertically {%- endcomment -%}
          <div class="slideshow-room-mobile-layout" style="display: none;">
            <div class="slideshow-room-mobile-images">
              {%- for block in section.blocks -%}
                <div class="slideshow-room-mobile-image">
                  {%- if block.settings.image != blank -%}
                    {%- if block.settings.link != blank -%}
                      <a href="{{ block.settings.link }}" class="hero__image-link" aria-hidden="true">
                    {%- endif -%}

                    {%- capture image_classes -%}
                        hero__image hero__image--{{ block.id }}
                      {%- endcapture -%}

                    {%- liquid
                      if forloop.index == 1
                        assign loading = lazyload_images
                      else
                        assign loading = true
                      endif
                    -%}
                    {%- render 'image-element',
                      img: block.settings.image,
                      loading: loading,
                      classes: image_classes,
                      sizeVariable: '100vw'
                    -%}

                    {%- if block.settings.link != blank -%}
                      </a>
                    {%- endif -%}
                  {%- else -%}
                    {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
                  {%- endif -%}

                  {%- comment -%} Image title - visible on mobile {%- endcomment -%}
                  {%- if block.settings.title != blank -%}
                    <div class="slideshow-room-image-title">
                      {{ block.settings.title }}
                    </div>
                  {%- endif -%}
                </div>
              {%- endfor -%}
            </div>

            {%- comment -%} Right side static image for mobile {%- endcomment -%}
            <div class="slideshow-room-mobile-right">
              {%- if section.settings.right_image != blank -%}
                {%- if section.settings.right_link != blank -%}
                  <a href="{{ section.settings.right_link }}" class="hero__image-link" aria-hidden="true">
                {%- endif -%}

                {%- capture right_image_classes -%}
                    hero__image hero__image--right-static
                  {%- endcapture -%}

                {%- render 'image-element',
                  img: section.settings.right_image,
                  loading: lazyload_images,
                  classes: right_image_classes,
                  sizeVariable: '100vw'
                -%}

                {%- if section.settings.right_link != blank -%}
                  </a>
                {%- endif -%}
              {%- else -%}
                {%- render 'placeholder-svg', name: 'lifestyle-2' -%}
              {%- endif -%}
            </div>
          </div>

          {%- comment -%} Desktop slideshow layout {%- endcomment -%}
          <div
            id="Slideshow-{{ section.id }}"
            class="hero hero--{{ section.id }} slideshow-room-hero--{{ section.id }}{% if section.index == 1 %} loaded{% else %} loading{% endif %} loading--delayed slideshow-room-desktop-layout"
            data-slide-count="{{ section.blocks.size }}"
            data-dots="true"
          >
            {%- for block in section.blocks -%}
              <div
                {{ block.shopify_attributes }}
                class="slideshow__slide slideshow__slide--{{ block.id }}{% if section.index == 1 and forloop.index == 1 %} is-selected{% endif %}"
                data-index="{{ forloop.index0 }}"
                data-id="{{ block.id }}"
              >


                <div class="hero__dual-images">
                  <div class="hero__left-image">
                    {%- if block.settings.image != blank -%}
                      {%- if block.settings.link != blank -%}
                        <a href="{{ block.settings.link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture image_classes -%}
                          hero__image hero__image--{{ block.id }}
                        {%- endcapture -%}

                      {%- liquid
                        if forloop.index == 1
                          assign loading = lazyload_images
                        else
                          assign loading = true
                        endif
                      -%}
                      {%- render 'image-element',
                        img: block.settings.image,
                        loading: loading,
                        classes: image_classes,
                        sizeVariable: '647px'
                      -%}

                      {%- if block.settings.link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
                    {%- endif -%}

                    {%- comment -%} Image title - only visible on mobile {%- endcomment -%}
                    {%- if block.settings.title != blank -%}
                      <div class="slideshow-room-image-title">
                        {{ block.settings.title }}
                      </div>
                    {%- endif -%}
                  </div>

                  {%- comment -%} Right side static image - same for all slides {%- endcomment -%}
                  <div class="hero__right-image">
                    {%- if section.settings.right_image != blank -%}
                      {%- if section.settings.right_link != blank -%}
                        <a href="{{ section.settings.right_link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture right_image_classes -%}
                          hero__image hero__image--right-static
                        {%- endcapture -%}

                      {%- render 'image-element',
                        img: section.settings.right_image,
                        loading: lazyload_images,
                        classes: right_image_classes,
                        sizeVariable: '1015px'
                      -%}

                      {%- if section.settings.right_link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-2' -%}
                    {%- endif -%}
                  </div>
                </div>
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- render 'placeholder-noblocks' -%}
    </div>
    {%- unless full_width -%}
      </div>
    {%- endunless -%}
  </slideshow-section>


</is-land>
